#!/usr/bin/env python3
"""
Test script to demonstrate the new Google Sheets functionalities:
1. Creating a new sheet
2. Writing content to the sheet

This script shows how to use the new methods directly and through the agent.
"""

import json
from sheets.sheets_app import SheetsApp
from agents.sheets_agent import SheetsReActAgent

def test_sheets_app_directly():
    """Test the SheetsApp methods directly"""
    print("=== Testing SheetsApp methods directly ===")
    
    sheets_app = SheetsApp()
    
    # Test creating a new sheet
    print("\n1. Creating a new sheet...")
    sheet_name = "Test Sheet - Direct API"
    result = sheets_app.create_sheet(sheet_name)
    print(f"Create sheet result: {json.dumps(result, indent=2)}")
    
    if "spreadsheet_id" in result:
        spreadsheet_id = result["spreadsheet_id"]
        
        # Test writing data to the sheet
        print("\n2. Writing data to the sheet...")
        test_data = [
            ["Name", "Age", "City"],
            ["Alice", "25", "New York"],
            ["<PERSON>", "30", "San Francisco"],
            ["<PERSON>", "35", "Chicago"]
        ]
        
        write_result = sheets_app.write_to_sheet(
            spreadsheet_id=spreadsheet_id,
            sheet_name="Sheet1",  # Default sheet name in new spreadsheets
            data=test_data,
            range_start="A1"
        )
        print(f"Write data result: {json.dumps(write_result, indent=2)}")
    else:
        print("Failed to create sheet, skipping write test")

def test_agent_tools():
    """Test the new tools through the agent"""
    print("\n\n=== Testing through SheetsReActAgent ===")
    
    try:
        agent = SheetsReActAgent()
        
        # Test creating a sheet through the agent
        print("\n1. Testing sheet creation through agent...")
        response1 = agent.run("Create a new Google Sheet called 'Agent Test Sheet'")
        print(f"Agent response for sheet creation:\n{response1}")
        
        # Test writing data through the agent
        print("\n2. Testing data writing through agent...")
        response2 = agent.run("""
        I want to write some sample data to a sheet. Can you help me write this data to any existing sheet:
        [["Product", "Price", "Stock"], ["Laptop", "999", "10"], ["Mouse", "25", "50"], ["Keyboard", "75", "30"]]
        
        If you need a spreadsheet ID, please list my sheets first and use any available one.
        """)
        print(f"Agent response for data writing:\n{response2}")
        
    except Exception as e:
        print(f"Error testing agent: {str(e)}")

def main():
    """Main function to run all tests"""
    print("Google Sheets New Features Test")
    print("=" * 50)
    
    # Check if user is authenticated
    sheets_app = SheetsApp()
    creds = sheets_app.load_credentials()
    
    if not creds:
        print("❌ User not authenticated!")
        print("Please authenticate first by:")
        print("1. Starting the FastAPI server: uvicorn main:app --reload")
        print("2. Visiting: http://localhost:8000/auth/google")
        print("3. Completing the OAuth flow")
        return
    
    print("✅ User is authenticated!")
    
    # Run tests
    try:
        test_sheets_app_directly()
        test_agent_tools()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed!")
        print("Check your Google Drive for the newly created sheets.")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")

if __name__ == "__main__":
    main()
