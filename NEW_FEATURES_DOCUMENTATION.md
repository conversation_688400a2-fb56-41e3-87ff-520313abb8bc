# Google Sheets New Features Documentation

This document describes the new functionalities added to the Google Sheets application:

## 🆕 New Methods in SheetsApp

### 1. `create_sheet(sheet_name: str)`

Creates a new Google Sheet with the specified name.

**Parameters:**
- `sheet_name` (str): The name for the new spreadsheet

**Returns:**
- `dict`: Contains the spreadsheet ID, URL, title, and success message

**Example:**
```python
from sheets.sheets_app import SheetsApp

sheets_app = SheetsApp()
result = sheets_app.create_sheet("My New Sheet")
print(result)
# Output:
# {
#   "spreadsheet_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
#   "spreadsheet_url": "https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit",
#   "title": "My New Sheet",
#   "message": "Successfully created spreadsheet 'My New Sheet'"
# }
```

### 2. `write_to_sheet(spreadsheet_id: str, sheet_name: str, data: list, range_start: str = "A1")`

Writes data to a specific sheet in a spreadsheet.

**Parameters:**
- `spreadsheet_id` (str): The ID of the spreadsheet
- `sheet_name` (str): The name of the sheet within the spreadsheet
- `data` (list): 2D list of data to write (rows and columns)
- `range_start` (str): Starting cell position (default: "A1")

**Returns:**
- `dict`: Contains information about the write operation

**Example:**
```python
data = [
    ["Name", "Age", "City"],
    ["Alice", "25", "New York"],
    ["Bob", "30", "San Francisco"]
]

result = sheets_app.write_to_sheet(
    spreadsheet_id="1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    sheet_name="Sheet1",
    data=data,
    range_start="A1"
)
print(result)
# Output:
# {
#   "updated_cells": 6,
#   "updated_range": "Sheet1!A1:C2",
#   "message": "Successfully wrote 6 cells to Sheet1!A1:C2"
# }
```

## 🛠️ New Agent Tools

### 1. `create_new_sheet`

A tool that allows the agent to create new Google Sheets.

**Usage through agent:**
```python
agent = SheetsReActAgent()
response = agent.run("Create a new Google Sheet called 'Sales Data 2024'")
```

### 2. `write_data_to_sheet`

A tool that allows the agent to write data to existing sheets.

**Usage through agent:**
```python
agent = SheetsReActAgent()
response = agent.run("""
Write this data to Sheet1 in spreadsheet 1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms:
[["Product", "Price"], ["Laptop", "999"], ["Mouse", "25"]]
""")
```

## 📋 Complete Workflow Examples

### Example 1: Create and Populate a New Sheet

```python
from sheets.sheets_app import SheetsApp

# Initialize the app
sheets_app = SheetsApp()

# Step 1: Create a new sheet
create_result = sheets_app.create_sheet("Employee Database")
spreadsheet_id = create_result["spreadsheet_id"]

# Step 2: Add data to the sheet
employee_data = [
    ["Employee ID", "Name", "Department", "Salary"],
    ["001", "John Doe", "Engineering", "75000"],
    ["002", "Jane Smith", "Marketing", "65000"],
    ["003", "Bob Johnson", "Sales", "60000"]
]

write_result = sheets_app.write_to_sheet(
    spreadsheet_id=spreadsheet_id,
    sheet_name="Sheet1",
    data=employee_data
)

print(f"Created sheet: {create_result['spreadsheet_url']}")
print(f"Added {write_result['updated_cells']} cells")
```

### Example 2: Using the Agent for Natural Language Interaction

```python
from agents.sheets_agent import SheetsReActAgent

agent = SheetsReActAgent()

# Create a sheet using natural language
response1 = agent.run("Please create a new spreadsheet called 'Monthly Budget'")
print(response1)

# Add data using natural language
response2 = agent.run("""
I want to add budget data to my Monthly Budget sheet. Please write this data:
[["Category", "Budgeted", "Actual"], ["Food", "500", "450"], ["Transport", "200", "180"], ["Entertainment", "150", "200"]]
""")
print(response2)
```

## 🔧 API Integration

The new functionalities are also available through the FastAPI endpoints. You can extend the main.py file to add new endpoints:

```python
@app.post("/sheets/create")
def create_sheet_endpoint(request: CreateSheetRequest):
    result = sheets_app.create_sheet(request.sheet_name)
    return result

@app.post("/sheets/{spreadsheet_id}/write")
def write_to_sheet_endpoint(spreadsheet_id: str, request: WriteDataRequest):
    result = sheets_app.write_to_sheet(
        spreadsheet_id=spreadsheet_id,
        sheet_name=request.sheet_name,
        data=request.data,
        range_start=request.range_start
    )
    return result
```

## 🧪 Testing

Run the test script to verify the new functionalities:

```bash
python test_new_features.py
```

Make sure you're authenticated before running the tests!

## 📝 Notes

- Both methods require proper Google Sheets API authentication
- The `create_sheet` method creates a spreadsheet with a default "Sheet1" tab
- The `write_to_sheet` method uses "RAW" value input option by default
- Error handling is built into both methods
- The agent tools provide a natural language interface to these functionalities
