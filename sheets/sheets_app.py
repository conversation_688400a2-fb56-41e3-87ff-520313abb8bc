import os
import pickle
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import Flow
from fastapi.responses import JSONResponse

CLIENT_SECRETS_FILE = "credentials.json"

SCOPES = [
    "https://www.googleapis.com/auth/drive",
    "https://www.googleapis.com/auth/drive.metadata.readonly",
    "https://www.googleapis.com/auth/spreadsheets"
]

REDIRECT_URI = "http://localhost:8000/auth/callback"


class SheetsApp:
    def __init__(self):
        self.token_file = "token.pickle"

    def get_flow(self):
        return Flow.from_client_secrets_file(
            CLIENT_SECRETS_FILE,
            scopes=SCOPES,
            redirect_uri=REDIRECT_URI,
        )

    def save_credentials(self, creds):
        with open(self.token_file, "wb") as token_file:
            pickle.dump(creds, token_file)

    def load_credentials(self):
        if not os.path.exists(self.token_file):
            return None
        with open(self.token_file, "rb") as token_file:
            return pickle.load(token_file)

    def list_sheets(self):
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("drive", "v3", credentials=creds)
        results = service.files().list(
            q="mimeType='application/vnd.google-apps.spreadsheet'",
            fields="files(id, name)"
        ).execute()

        return results.get("files", [])

    def get_sheet_details(self, spreadsheet_id: str):
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("sheets", "v4", credentials=creds)
        sheet_metadata = (
            service.spreadsheets()
            .get(spreadsheetId=spreadsheet_id)
            .execute()
        )
        return {"sheets": sheet_metadata.get("sheets", []) }

    def get_sheet_values(self, spreadsheet_id: str, sheet_id: int):
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("sheets", "v4", credentials=creds)

        # Step 1: Fetch metadata to find sheet title by sheetId
        metadata = (
            service.spreadsheets()
            .get(spreadsheetId=spreadsheet_id)
            .execute()
        )

        sheet_title = None
        for sheet in metadata.get("sheets", []):
            if sheet["properties"]["sheetId"] == sheet_id:
                sheet_title = sheet["properties"]["title"]
                break

        if not sheet_title:
            return JSONResponse({"error": f"Sheet with id {sheet_id} not found"}, status_code=404)

        # Step 2: Fetch values from the sheet using the title
        result = (
            service.spreadsheets()
            .values()
            .get(spreadsheetId=spreadsheet_id, range=sheet_title)
            .execute()
        )

        return result.get("values", [])

    def create_sheet(self, sheet_name: str):
        """Create a new Google Sheet with the given name.

        Args:
            sheet_name (str): The name for the new spreadsheet

        Returns:
            dict: Contains the spreadsheet ID and URL of the created sheet
        """
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("sheets", "v4", credentials=creds)

        # Create the spreadsheet
        spreadsheet_body = {
            "properties": {
                "title": sheet_name
            }
        }

        try:
            spreadsheet = service.spreadsheets().create(body=spreadsheet_body).execute()
            spreadsheet_id = spreadsheet.get("spreadsheetId")
            spreadsheet_url = spreadsheet.get("spreadsheetUrl")

            return {
                "spreadsheet_id": spreadsheet_id,
                "spreadsheet_url": spreadsheet_url,
                "title": sheet_name,
                "message": f"Successfully created spreadsheet '{sheet_name}'"
            }
        except Exception as e:
            return {"error": f"Failed to create spreadsheet: {str(e)}"}

    def write_to_sheet(self, spreadsheet_id: str, sheet_name: str, data: list, range_start: str = "A1"):
        """Write data to a specific sheet in a spreadsheet.

        Args:
            spreadsheet_id (str): The ID of the spreadsheet
            sheet_name (str): The name of the sheet within the spreadsheet
            data (list): 2D list of data to write (rows and columns)
            range_start (str): Starting cell position (default: "A1")

        Returns:
            dict: Contains information about the write operation
        """
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("sheets", "v4", credentials=creds)

        # Construct the range
        range_name = f"{sheet_name}!{range_start}"

        # Prepare the data
        value_input_option = "RAW"  # or "USER_ENTERED" for formula interpretation
        body = {
            "values": data
        }

        try:
            result = service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption=value_input_option,
                body=body
            ).execute()

            updated_cells = result.get("updatedCells", 0)
            updated_range = result.get("updatedRange", "")

            return {
                "updated_cells": updated_cells,
                "updated_range": updated_range,
                "message": f"Successfully wrote {updated_cells} cells to {updated_range}"
            }
        except Exception as e:
            return {"error": f"Failed to write to sheet: {str(e)}"}


